import React, { memo, useMemo } from "react";
import LoadingSpinner from "../ui/LoadingSpinner";
import PDFHighlighter from "../features/PDFHighlighter";
import "../../styles/MainContent.css";

const MainContent = memo(({ topic, textSize }) => {
  // Get PDF URL - simplified since we only handle PDFs
  const pdfUrl = useMemo(() => {
    // Check direct pdfUrl property first
    if (topic?.pdfUrl) return topic.pdfUrl;

    // Check content body for PDF content
    const pdfContent = topic?.content?.body?.find(element => element.type === "pdf");
    return pdfContent?.pdfUrl;
  }, [topic?.pdfUrl, topic?.content?.body]);

  if (!topic) {
    return (
      <main className="main-content-panel">
        <LoadingSpinner size="large" message="Loading content..." />
      </main>
    );
  }

  return (
    <main className={`main-content-panel text-size-${textSize}`}>
      <article className="lesson-content">
        <h1>{topic.content?.heading || topic.title}</h1>

        {/* Render PDF content */}
        <div className="lesson-content-body">
          {pdfUrl ? (
            <PDFHighlighter
              pdfUrl={pdfUrl}
              pageId={topic.id}
              topicId={topic.realTopicId || topic.id}
              textSize={textSize}
            />
          ) : (
            <div className="no-content">
              <p>No PDF available for this topic.</p>
            </div>
          )}
        </div>
      </article>
    </main>
  );
});

// Display name for debugging
MainContent.displayName = "MainContent";

export default MainContent;
