# Source Code Structure

This document outlines the improved folder structure following React best practices.

## Directory Structure

```
src/
├── components/           # All React components
│   ├── ui/              # Reusable UI components
│   ├── layout/          # Layout and structural components
│   ├── features/        # Feature-specific components
│   ├── common/          # Shared utility components
│   └── index.js         # Main components export
├── pages/               # Page-level components (routes)
├── hooks/               # Custom React hooks
├── services/            # API services and external integrations
├── stores/              # State management (Zustand stores)
├── routes/              # Route configuration and guards
├── utils/               # Utility functions and constants
├── styles/              # Global styles and CSS files
├── assets/              # Static assets (images, icons, etc.)
├── config/              # Configuration files
├── App.jsx              # Main application component
└── main.jsx             # Application entry point
```

## Component Categories

### UI Components (`src/components/ui/`)
Reusable interface elements that can be used across the application:
- `LoadingSpinner.jsx` - Loading indicators and skeletons
- `Toast.jsx` - Notification components
- `ColorPicker.jsx` - Color selection component
- `RichTextEditor.jsx` - Rich text editing component

### Layout Components (`src/components/layout/`)
Components that define the application structure and layout:
- `Header.jsx` - Application header
- `CourseStructure.jsx` - Course navigation sidebar
- `MainContent.jsx` - Main content area wrapper
- `ToolsPanel.jsx` - Tools and controls sidebar

### Feature Components (`src/components/features/`)
Business logic specific components:
- `PDFHighlighter.jsx` - PDF viewing and highlighting
- `BookmarkAnnotationPanel.jsx` - Bookmarks and annotations management

### Common Components (`src/components/common/`)
Shared utility components:
- `ErrorBoundary.jsx` - Error handling wrapper
- `AnimationBox.jsx` - Animation wrapper component

### Pages (`src/pages/`)
Route-level components that represent full pages:
- `LoginPage.jsx` - Authentication page
- `ReadingPage.jsx` - Main reading interface

## Import Patterns

### Using Index Files
Each component directory has an `index.js` file for cleaner imports:

```javascript
// Instead of:
import LoadingSpinner from './components/ui/LoadingSpinner';
import Toast from './components/ui/Toast';

// You can use:
import { LoadingSpinner, Toast } from './components/ui';
```

### Relative Imports
Components use relative imports to reference other components:
- `../ui/ComponentName` - From layout to ui
- `../../stores/storeName` - From components to stores
- `../../styles/FileName.css` - From components to styles

## Benefits of This Structure

1. **Scalability** - Easy to add new components in appropriate categories
2. **Maintainability** - Clear separation of concerns
3. **Reusability** - UI components can be easily reused
4. **Developer Experience** - Intuitive file organization
5. **Testing** - Components can be tested in isolation
6. **Code Splitting** - Better support for lazy loading

## Style Organization

All CSS files are centralized in `src/styles/` directory:
- Component-specific styles follow the naming pattern: `ComponentName.css`
- Global styles are in `App.css`
- This makes it easier to manage and avoid style conflicts
